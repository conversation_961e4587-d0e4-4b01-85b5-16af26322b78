<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Filament\Support\RawJs;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('type')
                    ->options(['Marché' => 'Marché', 'B_commande' => 'B commande', 'Achat_direct' => 'Achat direct'])
                    ->default('Marché')
                    ->required(),
                TextInput::make('subject')
                    ->required(),
                TextInput::make('reference')
                    ->required(),
                DatePicker::make('approb_date')
                    ->required(),
                Select::make('supplier_id')
                    ->relationship('supplier', 'name')
                    ->searchable()
                    ->required(),
                TextInput::make('total')
                    ->required()
                    ->mask(RawJs::make('$money($input, \',\', \' \', 3)'))
                    ->formatStateUsing(fn(?string $state): ?string => $state ? number_format($state, 3, ',', ' ') : null)
                    ->dehydrateStateUsing(fn(?string $state): ?string => $state ? str_replace([' ', ','], ['', '.'], $state) : null),
                Select::make('status')
                    ->options([
                        'En_cours' => 'En cours',
                        'Annulée' => 'Annulée',
                        'Achevée' => 'Achevée',
                        'En_attente' => 'En attente',
                    ])
                    ->default('En_cours')
                    ->required(),
            ]);
    }
}
