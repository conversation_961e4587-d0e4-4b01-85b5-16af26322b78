<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'subject',
        'reference',
        'approb_date',
        'supplier_id',
        'total',
        'status',
    ];

    protected $casts = [
        'approb_date' => 'date',
        'total' => 'decimal:3',
    ];

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function lots(): HasMany
    {
        return $this->hasMany(Lot::class);
    }
}
