<?php
    use Illuminate\View\ComponentAttributeBag;

    use function Filament\Support\generate_icon_html;
?>

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'breadcrumbs' => [],
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'breadcrumbs' => [],
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<nav <?php echo e($attributes->class(['fi-breadcrumbs'])); ?>>
    <ol class="fi-breadcrumbs-list">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="fi-breadcrumbs-item">
                <!--[if BLOCK]><![endif]--><?php if(! $loop->first): ?>
                    <?php echo e(generate_icon_html(\Filament\Support\Icons\Heroicon::ChevronRight, alias: \Filament\Support\View\SupportIconAlias::BREADCRUMBS_SEPARATOR, attributes: (new ComponentAttributeBag)->class([
                            'fi-breadcrumbs-item-separator fi-ltr',
                        ]))); ?>


                    <?php echo e(generate_icon_html(\Filament\Support\Icons\Heroicon::ChevronLeft, alias: \Filament\Support\View\SupportIconAlias::BREADCRUMBS_SEPARATOR_RTL, attributes: (new ComponentAttributeBag)->class([
                            'fi-breadcrumbs-item-separator fi-rtl',
                        ]))); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if(is_int($url)): ?>
                    <span class="fi-breadcrumbs-item-label">
                        <?php echo e($label); ?>

                    </span>
                <?php else: ?>
                    <a
                        <?php echo e(\Filament\Support\generate_href_html($url)); ?>

                        class="fi-breadcrumbs-item-label"
                    >
                        <?php echo e($label); ?>

                    </a>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </ol>
</nav>
<?php /**PATH C:\xampp\htdocs\commandes_filament\vendor\filament\support\resources\views/components/breadcrumbs.blade.php ENDPATH**/ ?>