<?php

namespace App\Filament\Resources\Orders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'Marché' => 'M',
                        'B_commande' => 'BC',
                        'Achat_direct' => 'AD',
                        default => $state,
                    })->color(fn(string $state): string => match ($state) {
                        'Marché' => 'success',
                        'B_commande' => 'info',
                        'Achat_direct' => 'warning',
                        default => 'gray',
                    }),
                TextColumn::make('subject')
                    ->searchable()
                    ->limit(40)
                    ->tooltip(fn($record): string => $record->subject)
                    ->extraCellAttributes([
                        'dir' => 'rtl',
                        'style' => 'text-align: right;',
                    ]),
                TextColumn::make('reference')
                    ->searchable(),
                TextColumn::make('approb_date')
                    ->date()
                    ->sortable(),
                TextColumn::make('supplier.name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('total')
                    ->money()
                    ->formatStateUsing(fn($state) => number_format($state, 3, ',', '.'))
                    ->suffix(' DT') // Optionnel
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => $state === 'En_cours' ? 'primary' : ($state === 'Annulée' ? 'danger' : ($state === 'Achevée' ? 'success' : 'warning')))
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
